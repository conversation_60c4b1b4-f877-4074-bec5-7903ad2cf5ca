"use client";

// import type { Metadata } from 'next';
import { useCallback, useEffect, useRef, useState } from 'react';
import { convertApiToPageData } from 'apis/api';

import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { IconInfoCircle } from '@tabler/icons-react';
import Popup from 'reactjs-popup';
import { PageData, PageObject } from 'models/page_objects';


// import { SITE } from '~/config.js';

// export const metadata: Metadata = {
//   title: SITE.title,
// };

export default function Page() {
  const [pageData, setPageData] = useState<PageData | null>(null);
  // const [paramLoaded,setLoadedParams] = useState<String[]>([]);

  const paramLoaded = useRef<String[]>([]);
  const searchParams = useSearchParams();

  
  const loadInitialData = useCallback((_path:String,paramAlreadyLoaded:boolean)=>{   
    // if (pageData!=null){
      setPageData(null);
    // }
    convertApiToPageData(_path,paramAlreadyLoaded).then(
      (data) => setPageData(data)
    )
  },[]);

  useEffect(() => {
    const path = searchParams.get('page') ?? 'home';
    console.log(`Getting data for ${path}`)
    const paramAlreadyLoaded = paramLoaded.current.some((param)=> param===path);
    if (!paramAlreadyLoaded){
      paramLoaded.current=[...paramLoaded.current,path];
    }
    loadInitialData(path,paramAlreadyLoaded);
  }, [loadInitialData, searchParams]);

  return (
    <div className='min-h-screen'>
      {pageData?.title && (
        <div className='flex ml-4 sm:ml-6 md:ml-12 lg:ml-16 mt-6 sm:mt-8 mb-6 sm:mb-8 text-xl sm:text-2xl md:text-3xl font-bold items-center animate-fade-in'>
          <span className="capitalize font-display text-gradient-primary">
            🗺️ {pageData.title?.replace(/-/g, ' ')}
          </span>
          {pageData.icon && (
            <Popup
              trigger={() => <IconInfoCircle className="ml-3 cursor-pointer text-neutral-500 hover:text-primary-500 transition-colors duration-200" />}
              on={['hover', 'focus']}
              arrowStyle={{ color: 'rgb(255,255,255,1)' }}
              contentStyle={{
                background: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.95) 100%)',
                color: '#1e293b',
                borderColor: 'rgba(226, 232, 240, 0.5)',
                borderRadius: '1rem',
                maxWidth: '400px',
                padding: '20px',
                border: '1px solid',
                fontFamily: 'var(--font-custom), system-ui, -apple-system, sans-serif',
                backdropFilter: 'blur(12px)',
                boxShadow: '0 10px 40px -10px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
              }}
              overlayStyle={{ color: 'rgba(0,0,0,0.3)' }}
            >
              <div className="text-sm leading-relaxed">{pageData.icon}</div>
            </Popup>
          )}
        </div>
      )}
      <div className='px-4 sm:px-6 md:px-12 lg:px-16 pb-12 sm:pb-16'>
        {
          (pageData === null) ? (
            <div className='flex h-[50vh] sm:h-[60vh] justify-center items-center'>
              <div className="flex flex-col items-center space-y-4">
                <div className="spinner h-10 w-10 sm:h-12 sm:w-12"></div>
                <p className="text-neutral-600 dark:text-neutral-400 font-medium text-sm sm:text-base">Loading amazing maps...</p>
              </div>
            </div>
          ) : (
            <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6 lg:gap-8 animate-fade-in'>
              {pageData.Objects.map((page, index) =>
                index === 0 ? (
                  <div key={page.id} className="sm:col-span-2 lg:col-span-2 xl:col-span-2">
                    <DoubleCard page={page} />
                  </div>
                ) : (
                  <NormalCard key={page.id} page={page} />
                )
              )}
            </div>
          )
        }
      </div>
    </div>
  );
}

const getAltImgLink = (imageLink: string): string|null => {

  const githubRawRegex = /^https:\/\/raw\.githubusercontent\.com\/([^/]+)\/([^/]+)\/(.+)$/;
  const githubRawMatch = imageLink.match(githubRawRegex);

  if (githubRawMatch) {
    const [, owner, repo, filePath] = githubRawMatch;
    return `https://${owner}.github.io/${repo}/${filePath}`;
  } else {
    const githubPageRegex = /^https:\/\/([^/]+)\.github\.io\/([^/]+)\/(.+)$/;
    const githubPageMatch = imageLink.match(githubPageRegex);

    if (githubPageMatch) {
      const [, owner, repo, filePath] = githubPageMatch;
      return `https://raw.githubusercontent.com/${owner}/${repo}/master/${filePath}`;
    }
  }

  // If the input doesn't match either pattern, return the input itself
  return null;
};


function DoubleCard(props: { page: PageObject }) {
  const page = props.page;
  const altImg = getAltImgLink(page.img);
  const href = (page.type === 'page') ? { pathname: '/', query: { page: page.id } } : { pathname: 'https://garudadevdataservices.github.io/map', query: { map: page.id } }

  return (
    <Link
      href={href}
      title={page.title}
      className="group block w-full focus-ring rounded-3xl"
      aria-label={`View ${page.title} ${page.type === 'page' ? 'map collection' : 'map'}`}
    >
      <div className='card-elevated card-interactive h-72 sm:h-80 lg:h-72 flex flex-col lg:flex-row overflow-hidden'>
        <div className='relative lg:w-2/3 w-full flex-shrink-0'>
          {(page.type === 'page') && (
            <div className='absolute top-4 right-4 z-10 rounded-full text-white px-3 py-1.5 text-xs font-semibold bg-neutral-900/70 backdrop-blur-sm border border-white/20'>
              🗺️ Map Collection
            </div>
          )}
          <img
            src={page.img}
            alt={altImg || page.title}
            className='w-full h-40 sm:h-48 lg:h-full object-cover transition-transform duration-500 ease-out group-hover:scale-110'
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          {page.tag && tag(page.tag, true)}
        </div>
        <div className='flex-1 flex items-center justify-center p-6 lg:p-8 bg-gradient-surface'>
          <div className='text-center'>
            <h3 className='text-lg lg:text-xl font-bold text-neutral-900 dark:text-white leading-tight group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-200'>
              {page.title}
            </h3>
            <div className="mt-2 w-12 h-0.5 bg-gradient-to-r from-primary-500 to-accent-500 mx-auto transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300" />
          </div>
        </div>
      </div>
    </Link>
  );
}


function NormalCard(props: { page: PageObject }) {
  const page = props.page;
  const altImg = getAltImgLink(page.img);
  const href = (page.type === 'page') ? { pathname: '/', query: { page: page.id } } : { pathname: 'https://garudadevdataservices.github.io/map', query: { map: page.id } }

  return (
    <Link href={href} title={page.title} className="group block">
      <div className='card-elevated card-interactive h-72 sm:h-80 flex flex-col overflow-hidden'>
        <div className='relative w-full flex-shrink-0'>
          {(page.type === 'page') && (
            <div className='absolute top-3 right-3 z-10 rounded-full text-white px-2.5 py-1 text-xs font-semibold bg-neutral-900/70 backdrop-blur-sm border border-white/20'>
              🗺️ Collection
            </div>
          )}
          <img
            src={page.img}
            alt={altImg || page.title}
            className='w-full h-40 sm:h-48 object-cover transition-transform duration-500 ease-out group-hover:scale-110'
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          {page.tag && tag(page.tag)}
        </div>
        <div className='flex-1 flex items-center justify-center p-4 bg-gradient-surface'>
          <div className='text-center w-full'>
            <h3 className='text-base font-semibold text-neutral-900 dark:text-white leading-tight group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-200 line-clamp-2'>
              {page.title}
            </h3>
            <div className="mt-2 w-8 h-0.5 bg-gradient-to-r from-primary-500 to-accent-500 mx-auto transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300" />
          </div>
        </div>
      </div>
    </Link>
  );
}

function tag(_tag: string, isRounded?: boolean) {
  const baseClasses = "absolute bottom-3 right-3 px-3 py-1.5 flex items-center gap-1.5 text-xs font-semibold shadow-medium backdrop-blur-sm border transition-all duration-200 hover:scale-105";
  const roundedClass = isRounded ? 'rounded-full' : 'rounded-l-full rounded-r-lg';

  switch (_tag) {
    case "politics":
      return (
        <div className={`${baseClasses} ${roundedClass} tag-politics`}>
          <span>🏛️</span>
          <span>ELECTIONS</span>
        </div>
      );
    case "religion":
      return (
        <div className={`${baseClasses} ${roundedClass} tag-religion`}>
          <span>☸️</span>
          <span>RELIGION</span>
        </div>
      );
    case "language":
      return (
        <div className={`${baseClasses} ${roundedClass} tag-language`}>
          <span>🗣️</span>
          <span>LANGUAGE</span>
        </div>
      );
    default:
      return (
        <div className={`${baseClasses} ${roundedClass} tag-default`}>
          <span>{_tag.toUpperCase()}</span>
        </div>
      );
  }
}

