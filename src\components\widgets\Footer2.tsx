import Link from 'next/link';
import { footerData2 } from '~/shared/data/global.data';

const Footer2 = () => {
  const { links, socials } = footerData2;

  return (
    <footer className="border-t border-neutral-200/50 dark:border-neutral-700/50 bg-white/80 dark:bg-neutral-900/80 backdrop-blur-md">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="py-8 md:py-12">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-6 md:space-y-0">

            {/* Social Links */}
            <div className="flex justify-center md:justify-start">
              <ul className="flex space-x-2">
                {socials.map(({ label, icon: Icon, href }, index) => (
                  <li key={`item-social-${index}`}>
                    <a
                      className="inline-flex items-center rounded-xl p-3 text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-all duration-200 ease-out focus-ring"
                      aria-label={label}
                      href={href}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {Icon && <Icon className="h-5 w-5" />}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Footer Links */}
            <div className="flex justify-center md:justify-end">
              <ul className="flex flex-wrap justify-center md:justify-end items-center space-x-1 text-sm">
                {links &&
                  links.map(({ label, href }, index) => (
                    <li key={`item-link-${index}`} className="flex items-center">
                      <Link
                        className="px-3 py-2 rounded-lg text-neutral-600 dark:text-neutral-400 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-all duration-200 ease-out focus-ring"
                        aria-label={label}
                        href={href as string}
                      >
                        {label}
                      </Link>
                      {links.length - 1 !== index && (
                        <span className="text-neutral-400 dark:text-neutral-600 mx-1">·</span>
                      )}
                    </li>
                  ))}
              </ul>
            </div>
          </div>

          {/* Copyright */}
          <div className="mt-8 pt-6 border-t border-neutral-200/50 dark:border-neutral-700/50">
            <p className="text-center text-sm text-neutral-500 dark:text-neutral-400">
              © {new Date().getFullYear()} MapExpo. Discovering the world through maps.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer2;
