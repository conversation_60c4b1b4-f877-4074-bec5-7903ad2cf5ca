@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* Modern CSS custom properties for consistent theming */
  :root {
    --gradient-primary: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);
    --gradient-secondary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    --gradient-accent: linear-gradient(135deg, #d946ef 0%, #c026d3 100%);
    --gradient-surface: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    --gradient-dark: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  /* Smooth scrolling and better font rendering */
  html {
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Focus styles for better accessibility */
  *:focus-visible {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2 ring-offset-white dark:ring-offset-neutral-900;
  }
}

@layer components {
  /* Modern button components */
  .btn {
    @apply inline-flex items-center justify-center rounded-xl border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 hover:bg-neutral-50 dark:hover:bg-neutral-700 text-center text-sm font-medium text-neutral-700 dark:text-neutral-200 leading-snug shadow-soft hover:shadow-medium transition-all duration-200 ease-out focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 py-3 px-6 md:px-8;
  }

  .btn-ghost {
    @apply border-none bg-transparent text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-white hover:bg-neutral-100 dark:hover:bg-neutral-800 shadow-none;
  }

  .btn-primary {
    @apply border-primary-600 dark:border-primary-500 hover:border-primary-700 dark:hover:border-primary-400 bg-primary-600 dark:bg-primary-500 hover:bg-primary-700 dark:hover:bg-primary-400 font-semibold text-white shadow-medium hover:shadow-large transform hover:scale-[1.02] active:scale-[0.98];
  }

  .btn-secondary {
    @apply border-secondary-300 dark:border-secondary-600 bg-secondary-100 dark:bg-secondary-800 hover:bg-secondary-200 dark:hover:bg-secondary-700 text-secondary-700 dark:text-secondary-200 font-medium;
  }

  /* Modern card components */
  .card {
    @apply rounded-2xl backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-700/50 bg-white/80 dark:bg-neutral-900/80 shadow-soft hover:shadow-medium transition-all duration-300 ease-out px-6 py-8 w-full;
  }

  .card-elevated {
    @apply rounded-3xl backdrop-blur-md border border-neutral-200/30 dark:border-neutral-700/30 bg-white/90 dark:bg-neutral-900/90 shadow-large hover:shadow-glow transition-all duration-300 ease-out transform hover:scale-[1.02] hover:-translate-y-1;
  }

  .card-interactive {
    @apply cursor-pointer hover:shadow-glow-lg transition-all duration-300 ease-out transform hover:scale-[1.02] hover:-translate-y-2 active:scale-[0.98] active:translate-y-0;
  }

  /* Modern gradient backgrounds */
  .bg-gradient-primary {
    background: var(--gradient-primary);
  }

  .bg-gradient-secondary {
    background: var(--gradient-secondary);
  }

  .bg-gradient-surface {
    background: var(--gradient-surface);
  }

  .bg-gradient-dark {
    background: var(--gradient-dark);
  }

  /* Modern text styles */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-400 bg-clip-text text-transparent;
  }

  .text-gradient-accent {
    @apply bg-gradient-to-r from-accent-600 to-accent-400 bg-clip-text text-transparent;
  }

  /* Modern tag/badge component */
  .tag {
    @apply inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full text-xs font-semibold shadow-soft transition-all duration-200 ease-out;
  }

  .tag-politics {
    @apply bg-warning-100 text-warning-800 border border-warning-200 dark:bg-warning-900/30 dark:text-warning-300 dark:border-warning-700;
  }

  .tag-religion {
    @apply bg-error-100 text-error-800 border border-error-200 dark:bg-error-900/30 dark:text-error-300 dark:border-error-700;
  }

  .tag-language {
    @apply bg-success-100 text-success-800 border border-success-200 dark:bg-success-900/30 dark:text-success-300 dark:border-success-700;
  }

  .tag-default {
    @apply bg-neutral-100 text-neutral-700 border border-neutral-200 dark:bg-neutral-800 dark:text-neutral-300 dark:border-neutral-700;
  }

  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-neutral-200 border-t-primary-600;
  }

  /* Modern ribbon component */
  .ribbon {
    @apply absolute top-4 right-4 px-3 py-1 rounded-full text-xs font-bold uppercase bg-success-500 text-white shadow-medium transform rotate-12 hover:rotate-6 transition-transform duration-200;
  }

  /* Glassmorphism effect */
  .glass {
    @apply backdrop-blur-md bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10;
  }

  /* Modern focus ring */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-neutral-900;
  }

  /* Text utilities */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Scroll animations */
  .animate-on-scroll {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  }

  .animate-on-scroll.in-view {
    opacity: 1;
    transform: translateY(0);
  }

  /* Staggered animations */
  .stagger-1 { animation-delay: 0.1s; }
  .stagger-2 { animation-delay: 0.2s; }
  .stagger-3 { animation-delay: 0.3s; }
  .stagger-4 { animation-delay: 0.4s; }
  .stagger-5 { animation-delay: 0.5s; }

  /* Respect user's motion preferences */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }

    .card-interactive:hover {
      transform: none !important;
    }

    img {
      transition: none !important;
    }
  }
}
