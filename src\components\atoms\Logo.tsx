import Image from 'next/image';
import AppLogo from '~/assets/icons/logo.ico';

const Logo = () => (
  <span className="md:ml-2 gap-2 flex items-center self-center whitespace-nowrap text-xl font-bold text-neutral-900 dark:text-white md:text-lg group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-200">
    <div className="relative">
      <Image
        src={AppLogo.src}
        alt={'MapExpo Logo'}
        height={40}
        width={40}
        className='rounded-lg shadow-soft group-hover:shadow-medium transition-all duration-200'
      />
      <div className="absolute inset-0 bg-gradient-to-br from-primary-500/20 to-accent-500/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
    </div>
    <span className="text-gradient-primary font-display">MapExpo</span>
  </span>
);

export default Logo;
