{"name": "@garudadevdataservices/mapexpo", "description": "A template to make your website using Next.js + Tailwind CSS.", "version": "1.0.0-beta.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postbuild": "next-sitemap", "prettier": "prettier --write --ignore-unknown .", "prettier:check": "prettier --check --ignore-unknown ."}, "dependencies": {"@tabler/icons-react": "^2.42.0", "gray-matter": "^4.0.3", "markdown-it": "^13.0.2", "next": "^13.4.19", "next-themes": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-spinners": "^0.13.8", "reactjs-popup": "^2.0.6", "sharp": "^0.33.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@tailwindcss/typography": "^0.5.10", "@types/markdown-it": "^13.0.7", "@types/node": "20.10.1", "@types/react": "18.2.39", "@types/react-dom": "18.2.17", "autoprefixer": "^10.4.16", "eslint": "8.54.0", "eslint-config-next": "^14.0.3", "next-sitemap": "^4.2.3", "postcss": "^8.4.31", "prettier": "3.1.0", "prettier-plugin-tailwindcss": "0.5.7", "tailwindcss": "^3.3.5", "typescript": "^5.3.2"}, "engines": {"node": ">=18.17.0"}}