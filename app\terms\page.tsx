import type { Metadata } from 'next';

import fs from 'fs';
import path from 'path';
import md from 'markdown-it';

export const metadata: Metadata = {
  title: 'Terms and conditions',
};

const Page = () => {
  const filePath = path.join(process.cwd(), 'src/content/terms/terms.md');
  const fileContent = fs.readFileSync(filePath, 'utf8');

  return (
    <div
      className="prose-sm prose-headings:font-heading prose-headings:leading-tighter container prose mx-auto mt-8 max-w-3xl px-6 prose-headings:font-bold prose-headings:tracking-tighter prose-a:text-primary-600 prose-img:rounded-md prose-img:shadow-lg dark:prose-invert dark:prose-headings:text-slate-300 dark:prose-a:text-primary-400 sm:px-6 lg:prose-sm"
      dangerouslySetInnerHTML={{
        __html: md({
          html: true,
        }).render(fileContent),
      }}
    />
  );
};

export default Page;
